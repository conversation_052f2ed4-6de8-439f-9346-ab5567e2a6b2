import json
import os
import glob
import re
from datetime import datetime
import shutil

def parse_timestamp(timestamp_str):
    """Parse various timestamp formats and return a datetime object for sorting."""
    if not timestamp_str or timestamp_str == "Unknown":
        return datetime.min  # Put unknown timestamps at the beginning
    
    # Common patterns in Teams timestamps
    patterns = [
        r'(\d{1,2})\.(\d{1,2})\.\s*(\d{1,2}):(\d{1,2})',  # DD.MM. HH:MM
        r'(\d{1,2})\.(\d{1,2})\s*(\d{1,2}):(\d{1,2})',    # DD.MM HH:MM
        r'(\d{1,2}):(\d{1,2})',                            # HH:MM (assume current day)
    ]
    
    for pattern in patterns:
        match = re.search(pattern, timestamp_str)
        if match:
            groups = match.groups()
            if len(groups) == 4:  # DD.MM. HH:MM
                day, month, hour, minute = groups
                # Assume current year since it's not in the timestamp
                year = datetime.now().year
                try:
                    return datetime(int(year), int(month), int(day), int(hour), int(minute))
                except ValueError:
                    continue
            elif len(groups) == 2:  # HH:MM
                hour, minute = groups
                # Use a default date for time-only timestamps
                try:
                    return datetime(2000, 1, 1, int(hour), int(minute))
                except ValueError:
                    continue
    
    return datetime.min  # Fallback for unparseable timestamps

def parse_content_date(content):
    """Extract date from content like 'Donnerstag, 17. Juli' or 'Mittwoch, 24. Juli'."""
    if not content:
        return datetime.min
    
    # German month names
    months = {
        'januar': 1, 'februar': 2, 'märz': 3, 'april': 4, 'mai': 5, 'juni': 6,
        'juli': 7, 'august': 8, 'september': 9, 'oktober': 10, 'november': 11, 'dezember': 12
    }
    
    # Pattern for "Donnerstag, 17. Juli"
    pattern = r'(\w+),\s*(\d{1,2})\.\s*(\w+)'
    match = re.search(pattern, content.lower())
    
    if match:
        weekday, day, month_name = match.groups()
        month_num = months.get(month_name)
        if month_num:
            year = datetime.now().year
            try:
                return datetime(year, month_num, int(day))
            except ValueError:
                pass
    
    return datetime.min

def get_message_sort_key(message):
    """Generate a sort key for a message based on timestamp, fallback to content date."""
    timestamp = message.get('timestamp', '')
    
    # First try to parse the timestamp
    dt = parse_timestamp(timestamp)
    
    # If timestamp is unknown, try to extract date from content
    if dt == datetime.min:
        content = message.get('content', '')
        dt = parse_content_date(content)
    
    return dt

def sort_chat_file(file_path):
    """Sort messages in a single chat file by timestamp."""
    print(f"Processing: {os.path.basename(file_path)}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        if not messages:
            print(f"  No messages found in {file_path}")
            return
        
        # Sort messages by timestamp only
        sorted_messages = sorted(messages, key=get_message_sort_key)
        
        # Update message_id to reflect new order
        for i, msg in enumerate(sorted_messages):
            msg['message_id'] = i
        
        # Create backup
        backup_path = file_path + '.backup'
        shutil.copy2(file_path, backup_path)
        print(f"  Backup created: {os.path.basename(backup_path)}")
        
        # Save sorted messages
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(sorted_messages, f, indent=2, ensure_ascii=False)
        
        print(f"  Sorted {len(sorted_messages)} messages")
        
        # Show first and last message timestamps for verification
        if len(sorted_messages) > 1:
            first_ts = sorted_messages[0].get('timestamp', 'Unknown')
            last_ts = sorted_messages[-1].get('timestamp', 'Unknown')
            print(f"  First message: {first_ts}")
            print(f"  Last message: {last_ts}")
        
    except Exception as e:
        print(f"  Error processing {file_path}: {e}")

def main():
    # Directory containing the JSON files
    json_dir = r"C:\Users\<USER>\Documents\augment-projects\TeamsScrapper"
    
    # Find all JSON files (exclude backups and combined exports)
    pattern = os.path.join(json_dir, "*.json")
    json_files = glob.glob(pattern)
    
    # Filter out backup files and combined exports
    chat_files = [f for f in json_files 
                  if not f.endswith('.backup') 
                  and 'teams_export_' not in os.path.basename(f)
                  and 'image_summary' not in os.path.basename(f)]
    
    print(f"Found {len(chat_files)} chat files to sort")
    
    if not chat_files:
        print("No chat files found!")
        return
    
    # Ask for confirmation
    response = input(f"Sort {len(chat_files)} files? (y/N): ")
    if response.lower() != 'y':
        print("Cancelled.")
        return
    
    # Process each file
    for file_path in chat_files:
        sort_chat_file(file_path)
        print()  # Empty line for readability
    
    print("Sorting completed!")

if __name__ == "__main__":
    main()

