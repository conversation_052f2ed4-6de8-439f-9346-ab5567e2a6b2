#If VBA7 Then
    Private Declare PtrSafe Function OpenClipboard Lib "user32" (ByVal hwnd As LongPtr) As Long
    Private Declare PtrSafe Function RegisterClipboardFormat Lib "user32" Alias _
        "RegisterClipboardFormatA" (ByVal lpString As String) As Long
    Private Declare PtrSafe Function EmptyClipboard Lib "user32" () As Long
    Private Declare PtrSafe Function CloseClipboard Lib "user32" () As Long
    Private Declare PtrSafe Function GetClipboardData Lib "user32" ( _
        ByVal wFormat As Long) As LongPtr
    Private Declare PtrSafe Function SetClipboardData Lib "user32" ( _
        ByVal wFormat As Long, ByVal hMem As LongPtr) As LongPtr
    Private Declare PtrSafe Function GlobalAlloc Lib "kernel32.dll" (ByVal wFlags As Long, _
        ByVal dwBytes As Long) As LongPtr
    Private Declare PtrSafe Sub CopyMemory Lib "kernel32.dll" Alias "RtlMoveMemory" ( _
        ByVal Destination As LongPtr, Source As Any, ByVal Length As Long)
    Private Declare PtrSafe Function GlobalUnlock Lib "kernel32.dll" (ByVal hMem As LongPtr) As LongPtr
    Private Declare PtrSafe Function GlobalLock Lib "kernel32.dll" (ByVal hMem As LongPtr) As LongPtr
    Private Declare PtrSafe Function GlobalSize Lib "kernel32.dll" (ByVal hMem As LongPtr) As LongPtr
    Private Declare PtrSafe Function GlobalFree Lib "kernel32.dll" (ByVal hMem As LongPtr) As LongPtr
    Private Declare PtrSafe Function FindWindow Lib "user32" Alias "FindWindowA" _
        (ByVal lpClassName As String, ByVal lpWindowName As String) As LongPtr
#Else
    Private Declare Function OpenClipboard Lib "user32" (ByVal hwnd As Long) As Long
    Private Declare Function RegisterClipboardFormat Lib "user32" Alias _
        "RegisterClipboardFormatA" (ByVal lpString As String) As Long
    Private Declare Function EmptyClipboard Lib "user32" () As Long
    Private Declare Function CloseClipboard Lib "user32" () As Long
    Private Declare Function GetClipboardData Lib "user32" ( _
        ByVal wFormat As Long) As Long
    Private Declare Function SetClipboardData Lib "user32" ( _
        ByVal wFormat As Long, ByVal hMem As Long) As Long
    Private Declare Function GlobalAlloc Lib "kernel32.dll" (ByVal wFlags As Long, _
        ByVal dwBytes As Long) As Long
    Private Declare Sub CopyMemory Lib "kernel32.dll" Alias "RtlMoveMemory" ( _
        ByVal Destination As Long, Source As Any, ByVal Length As Long)
    Private Declare Function GlobalUnlock Lib "kernel32.dll" (ByVal hMem As Long) As Long
    Private Declare Function GlobalLock Lib "kernel32.dll" (ByVal hMem As Long) As Long
    Private Declare Function GlobalSize Lib "kernel32.dll" (ByVal hMem As Long) As Long
    Private Declare Function GlobalFree Lib "kernel32.dll" (ByVal hMem As Long) As Long
    Private Declare Function FindWindow Lib "user32" Alias "FindWindowA" _
        (ByVal lpClassName As String, ByVal lpWindowName As String) As Long
#End If

Private Const GMEM_DDESHARE = &H2000
Private Const GMEM_MOVEABLE = &H2
Sub AutoOpen()
    If ActiveDocument.Name <> "1381R22.ARCHD.3201.FeatureSetDocumentationTemplate.dotm" Then
        If ActiveDocument.BuiltInDocumentProperties("Keywords") <> "ReporterPLUS" Then
            ActiveDocument.BuiltInDocumentProperties("Keywords") = "ReporterPLUS"
            Application.ScreenUpdating = False
            
            updateDocProperties
            updateTableStyle
            updateBookmarksAndHyperlinks
            expandRTF
            expandHighlight1

            'save document
            ActiveDocument.Repaginate
            ActiveDocument.TablesOfContents(1).Update
            Documents.Save True, wdOriginalDocumentFormat
                
            ActiveDocument.TablesOfContents(1).Update
            Documents.Save True, wdOriginalDocumentFormat
        
            Application.ScreenUpdating = True
        Else
            ActiveDocument.Saved = True
        End If
    End If
End Sub
Sub expandHighlight1()
'added to highlight (bold and color) the text where no info is available
    Dim cont As Range
    Dim startWord As String, endWord As String
    
    startWord = "##BeginHighlight1##"
    endWord = "##EndHighlight1##"
    
    Set cont = ActiveDocument.Content
    cont.Find.ClearFormatting
    cont.Find.Replacement.ClearFormatting
        
    With cont.Find
        .Text = startWord & "*" & endWord
        .Forward = True
        .Wrap = wdFindContinue
        .MatchWildcards = True
    End With
    
    cont.Find.Execute
    While cont.Find.Found
        replaceString = Replace(Replace(cont.Text, startWord, ""), endWord, "")
        cont.Text = replaceString
        cont.Font.Bold = True
        cont.Font.Color = wdColorRed
        cont.Find.Execute
    Wend
End Sub

Sub updateTableStyle()
    Dim nextTable As Word.Table

    'apply own style to all tables
    For Each nextTable In ActiveDocument.Tables
        'nextTable.AllowAutoFit = True
        If ((nextTable.Uniform = True) And (nextTable.Borders.OutsideLineWidth = 0) And _
            (nextTable.ApplyStyleColumnBands = True) And (nextTable.ApplyStyleRowBands = True) And _
            (nextTable.ApplyStyleFirstColumn = False) And (nextTable.ApplyStyleLastColumn = False) And _
            (nextTable.ApplyStyleLastRow = False) And (nextTable.ApplyStyleHeadingRows = False)) Then

            nextTable.Style = "ReporterGrid"
            'nextTable.ApplyStyleHeadingRows = False
            'nextTable.ApplyStyleLastRow = False
            'nextTable.ApplyStyleFirstColumn = False
            'nextTable.ApplyStyleLastColumn = False
            nextTable.Rows(1).HeadingFormat = True
        End If
    Next nextTable
End Sub
Sub updateBookmarksAndHyperlinks()
    Dim moduleHeading As Word.Paragraph
    Dim moduleTitle As String
    Dim moduleName As String
    Dim Length As Integer
    
    Dim nextBookmark As Word.Bookmark
    Dim bookmarkName As String
    Dim bookmarkStart As String
    Dim bookmarkEnd As String
    Dim bookmarkList As New Collection
    Dim bookmarkVariant As Variant

    Dim elementCount As Integer
    Dim count As Integer

    
    'detect the module name
    Set moduleHeading = ActiveDocument.Paragraphs(3)
    moduleTitle = moduleHeading.Range.Text

    moduleName = moduleTitle
    Length = Len(moduleName)
    moduleName = Left(moduleName, Length - 1)

    'detect all ReporterPlus generated bookmarks
    For Each nextBookmark In ActiveDocument.Bookmarks
        bookmarkName = nextBookmark.Name
        bookmarkStart = Left(bookmarkName, 1)
        bookmarkEnd = Right(bookmarkName, Len(bookmarkName + "") - 1)

        If (bookmarkStart = "b" And IsNumeric(bookmarkEnd)) Then
            bookmarkList.Add (bookmarkName)
        End If
    Next nextBookmark

    'add bookmarks with the new name
    For Each bookmarkVariant In bookmarkList
        If ActiveDocument.Bookmarks.Exists(bookmarkVariant) Then
            ActiveDocument.Bookmarks(bookmarkVariant).Copy Name:=moduleName + "_" + bookmarkVariant
            ActiveDocument.Bookmarks(bookmarkVariant).Delete
        End If
    Next bookmarkVariant

    'add hyperlinks with the sub address (existing hyperlinks will be mapped)
    elementCount = ActiveDocument.Hyperlinks.count
    For count = 1 To elementCount
        bookmarkName = ActiveDocument.Hyperlinks(count).Name
        bookmarkStart = Left(bookmarkName, 1)
        bookmarkEnd = Right(bookmarkName, Len(bookmarkName + "") - 1)

        If (bookmarkStart = "b" And IsNumeric(bookmarkEnd)) Then
            ActiveDocument.Hyperlinks.Add Anchor:=ActiveDocument.Hyperlinks(count).Range, SubAddress:=moduleName + "_" + ActiveDocument.Hyperlinks(count).SubAddress
        End If
    Next count
End Sub
Sub expandRTF()
    Selection.MoveUp Unit:=wdLine, count:=1
    Selection.HomeKey Unit:=wdStory
    Selection.Find.ClearFormatting
    
    With Selection.Find
        .Text = "##BeginRTF##*##EndRTF##"
        .Replacement.Text = ""
        .Forward = True
        .Wrap = wdFindContinue
        .Format = False
        .MatchCase = False
        .MatchWholeWord = False
        .MatchAllWordForms = False
        .MatchSoundsLike = False
        .MatchWildcards = True
    End With
    
    Do While Selection.Find.Execute
        Dim rtfString As String
        'get the rtf and the wrapping text
        rtfString = Selection.Text
        'strip out the wrapper
        rtfString = Mid(rtfString, 13, Len(rtfString) - 23)
        
        'Copy the contents of the Rich Text to the clipboard
        Dim lSuccess As Long
        Dim lRTF As Long
        #If VBA7 Then
            Dim hGlobal As LongPtr
            Dim lpString As LongPtr
        #Else
            Dim hGlobal As Long
            Dim lpString As Long
        #End If

        'Additionally added in order to remove the CR/LF later
        'on. We delete the content and add the Text to identify
        'the end after the paste. "Then we are searching for that
        ' string and are able to remove it (in function removeCR())
        Selection.Delete
        Selection.Range.Text = "##EndRTF##"

        lSuccess = OpenClipboard(0&)
        lRTF = RegisterClipboardFormat("Rich Text Format")
        lSuccess = EmptyClipboard()
        hGlobal = GlobalAlloc(GMEM_MOVEABLE Or GMEM_DDESHARE, Len(rtfString))
        lpString = GlobalLock(hGlobal)

        Call CopyMemory(lpString, ByVal rtfString, Len(rtfString))
        Call GlobalUnlock(hGlobal)
        Call SetClipboardData(lRTF, hGlobal)
        Call CloseClipboard
        Call GlobalFree(hGlobal)
        
        Selection.Range.Paste
        Call removeCarriageReturn
    Loop

    Selection.MoveUp Unit:=wdLine, count:=1
    Selection.HomeKey Unit:=wdStory
End Sub
Private Sub removeCarriageReturn()
    With Selection.Find
        .Text = vbCr + "##EndRTF##"
    End With
    
    Selection.Find.Execute
    Selection.Delete

    With Selection.Find
        .Text = "##BeginRTF##*##EndRTF##"
    End With
End Sub
Public Sub updateDocProperties()
    Dim department As String
    Dim mail As String
    Dim creationDate As String
    Dim releaser As String
    Dim releaserDept As String
    Dim releaserMail As String
    Dim releaseDate As String

    Dim designation As String
    Dim classification As String
    Dim generationDate As String
    Dim templateNames As String
    Dim revisionNumber As String

    Dim mailFieldFound As Boolean
    Dim creationDateFieldFound As Boolean
    Dim releaserFieldFound As Boolean
    Dim releaserDeptFieldFound As Boolean
    Dim releaserMailFieldFound As Boolean
    Dim releaseDateFieldFound As Boolean

    Dim designationFieldFound As Boolean
    Dim classificationFieldFound As Boolean

    'Get the department value
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(3, 1).Range.Text), 10) = "department" Then
        department = Split(ActiveDocument.Tables.Item(1).Cell(3, 2).Range.Text, Chr$(13))(0)
    End If
    
    'Get the E-mail value
    mailFieldFound = False
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(4, 1).Range.Text), 6) = "e-mail" Then
        mail = Split(ActiveDocument.Tables.Item(1).Cell(4, 2).Range.Text, Chr$(13))(0)
        mailFieldFound = True
    End If

    'Get the creation date value
    creationDateFieldFound = False
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(5, 1).Range.Text), 13) = "creation date" Then
        creationDate = Split(ActiveDocument.Tables.Item(1).Cell(5, 2).Range.Text, Chr$(13))(0)
        creationDateFieldFound = True
    End If

    'Get the releaser value
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(6, 1).Range.Text), 8) = "releaser" Then
        releaser = Split(ActiveDocument.Tables.Item(1).Cell(6, 2).Range.Text, Chr$(13))(0)
        releaserFieldFound = True
    End If

    'Get the releaser department value
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(7, 1).Range.Text), 19) = "releaser department" Then
        releaserDept = Split(ActiveDocument.Tables.Item(1).Cell(7, 2).Range.Text, Chr$(13))(0)
        releaserDeptFieldFound = True
    End If
    
    'Get the releaser E-mail value
    releaserMailFieldFound = False
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(8, 1).Range.Text), 15) = "releaser e-mail" Then
        releaserMail = Split(ActiveDocument.Tables.Item(1).Cell(8, 2).Range.Text, Chr$(13))(0)
        releaserMailFieldFound = True
    End If

    'Get the release date value
    releaseDateFieldFound = False
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(9, 1).Range.Text), 12) = "release date" Then
        releaseDate = Split(ActiveDocument.Tables.Item(1).Cell(9, 2).Range.Text, Chr$(13))(0)
        releaseDateFieldFound = True
    End If

    'Get the designation value
    designationFieldFound = False
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(10, 1).Range.Text), 11) = "designation" Then
        designation = Split(ActiveDocument.Tables.Item(1).Cell(10, 2).Range.Text, Chr$(13))(0)
        designationFieldFound = True
    End If

    'Get the classification value
    classificationFieldFound = False
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(11, 1).Range.Text), 14) = "classification" Then
        classification = Split(ActiveDocument.Tables.Item(1).Cell(11, 2).Range.Text, Chr$(13))(0)
        classificationFieldFound = True
    End If

    'Get the design date value
    'If (isHistoryTableAvailable() = True) Then
    '    If (isReleaseDocument() = True) Then
    '        generationDate = getDateFromHistory(1)
    '    Else
    '        generationDate = getDateFromHistory(0)
    '    End If
    'Else
    '    generationDate = Format(Date, "yyyy-mm-dd")
    'End If

    'Get the release date value
    'If (isReleaseDocument() = True) Then
    '    releaseDate = getDateFromHistory(0)
    'End If

    'Set the generation date value
    generationDate = Format(Date, "yyyy-mm-dd")
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(16, 1).Range.Text), 15) = "generation date" Then
        ActiveDocument.Tables.Item(1).Cell(16, 2).Range.Text = generationDate
    End If

    'Set the revision number value for the word template
    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(22, 1).Range.Text), 27) = "reporterplus template files" Then
        templateNames = ActiveDocument.Tables.Item(1).Cell(22, 2).Range.Text
        templateNames = Left(templateNames, Len(templateNames) - 2)

        'revisionNumber = Split(Templates.Item(1).Application.Documents.Item(2).Tables.Item(2).Cell(6, 2).Range.Text, Chr$(13))(0)
        revisionNumber = Templates.Item(1).CustomDocumentProperties.Item(20).Value
        templateNames = Replace(templateNames, "(Revision:   1.0  )", "($Revision:   " + revisionNumber + "  $)")

        ActiveDocument.Tables.Item(1).Cell(22, 2).Range.Text = templateNames
    End If

    'Calculate the mail address in case not specified
    If (mail = "@continental-corporation.com") Then
        'If (isHistoryTableAvailable() = True) Then
        '    If (isReleaseDocument() = True) Then
        '        mail = getMailFromHistory(1)
        '    Else
        '        mail = getMailFromHistory(0)
        '    End If
        'Else
            mail = getMailFromAuthor()
        'End If
    End If

    'Calculate the releaser mail address in case not specified
    If (releaserMail = "@continental-corporation.com" Or releaserMail = "") Then
        'If (isReleaseDocument() = True) Then
        '    releaserMail = getMailFromHistory(0)
        'End If
            releaserMail = getMailFromReleaser()
    End If

    'Update the property value with the mail value
    If (mail <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "mail") Then
                dp.Value = mail
            End If
        Next dp
    End If

    'Update the property value with the creation date value
    If (creationDate <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "creationdate") Then
                dp.Value = creationDate
            End If
        Next dp
    End If

    'Update the property value with the department value
    If (department <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "abteilung" Or LCase(dp.Name) = "department") Then
                dp.Value = department
            End If
        Next dp
    End If

    'Update the property value with the releaser mail value
    If (releaserMail <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "releasermail") Then
                dp.Value = releaserMail
            End If
        Next dp
    End If

    'Update the property value with the release date value
    If (releaseDate <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "releasedate") Then
                dp.Value = releaseDate
            End If
        Next dp
    End If

    'Update the property value with the department value
    If (releaserDept <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "releaserdept") Then
                dp.Value = releaserDept
            End If
        Next dp
    End If

    'Update the property value with the releaser department value
    'If (releaserDept <> "") Then
        'For Each dp In ActiveDocument.CustomDocumentProperties
        '    If (LCase(dp.name) = "releaserdept") Then
        '        If (isReleaseDocument() = True) Then
        '            dp.Value = releaserDept
        '        Else
        '            dp.Value = ""
        '        End If
        '    End If
        'Next dp
    'End If
    
    'Update the property value with the mail value
    'If (releaserMail <> "") Then
        'For Each dp In ActiveDocument.CustomDocumentProperties
        '    If (LCase(dp.name) = "releasermail") Then
        '        If (isReleaseDocument() = True) Then
        '            dp.Value = releaserMail
        '        Else
        '            dp.Value = ""
        '        End If
        '    End If
        'Next dp
    'End If

    'Update the property value with the design date value
    'If (generationDate <> "") Then
    '    For Each dp In ActiveDocument.CustomDocumentProperties
    '        If (LCase(dp.name) = "designdate") Then
    '            dp.Value = generationDate
    '        End If
    '    Next dp
    'End If

    'Update the property value with the release date value
    'If (releaseDate <> "") Then
        'For Each dp In ActiveDocument.CustomDocumentProperties
        '    If (LCase(dp.name) = "releasedate") Then
        '        If (isReleaseDocument() = True) Then
        '            dp.Value = releaseDate
        '        Else
        '            dp.Value = ""
        '        End If
        '    End If
        'Next dp
    'End If

    'Update the property value with the designation value
    If (designation <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "designation") Then
                dp.Value = designation
            End If
        Next dp
    End If

    'Update the property value with the classification value
    If (classification <> "") Then
        For Each dp In ActiveDocument.CustomDocumentProperties
            If (LCase(dp.Name) = "classification") Then
                dp.Value = classification
            End If
        Next dp
    End If

    'Delete the classification row
    If classificationFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(11, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(11, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(11).Delete
    End If

    'Delete the designation row
    If classificationFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(10, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(10, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(10).Delete
    End If

    'Delete the release date row
    If classificationFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(9, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(9, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(9).Delete
    End If

    'Delete the releaser E-mail row
    If classificationFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(8, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(8, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(8).Delete
    End If

    'Delete the releaser department row
    If classificationFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(7, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(7, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(7).Delete
    End If

    'Delete the releaser row
    If designationFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(6, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(6, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(6).Delete
    End If

    'Delete the creation date row
    If releaserMailFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(5, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(5, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(5).Delete
    End If

    'Delete the E-mail row
    If releaserDeptFieldFound = True Then
        ActiveDocument.Tables.Item(1).Cell(4, 1).Range.Text = ""
        ActiveDocument.Tables.Item(1).Cell(4, 2).Range.Text = ""
        ActiveDocument.Tables.Item(1).Rows(4).Delete
    End If

    'Delete the department row
    'If depFieldFound = True Then
        'ActiveDocument.Tables.Item(1).Cell(2, 1).Range.Text = ""
        'ActiveDocument.Tables.Item(1).Cell(2, 2).Range.Text = ""
    'End If

    If (isHistoryTableAvailable() = True) Then
        removeReleaseColumnFromHistory
    End If

    'Update the fields in the document
    If (mail <> "" Or releaserMail <> "" Or creationDate <> "" Or releaseDate <> "" Or _
        department <> "" Or releaserDept <> "" Or designation <> "" Or classification <> "") Then
        'Update the fields
        'Application.ScreenUpdating = False
        'Dim sec As Section
        'ActiveDocument.Fields.Update
        ActiveDocument.Sections(1).Footers(wdHeaderFooterPrimary).Range.Fields.Update
        ActiveDocument.Sections(1).Footers(wdHeaderFooterPrimary).Range.Tables.Item(1).Cell(1, 1).Range.Bold = True
        
        'For Each sec In ActiveDocument.Sections
            'sec.Headers(wdHeaderFooterPrimary).Range.Fields.Update
            'sec.Headers(wdHeaderFooterFirstPage).Range.Fields.Update
            'sec.Footers(wdHeaderFooterPrimary).Range.Fields.Update
            'sec.Footers(wdHeaderFooterFirstPage).Range.Fields.Update
        'Next
    End If
End Sub
Function isHistoryTableAvailable() As Boolean
    Dim nextTable As Table
    Dim tableId As Integer
    Dim counter As Integer

    tableId = 2
    For Each nextTable In ActiveDocument.Tables
        counter = counter + 1
        If (counter > tableId) Then
            Exit For
        End If
        
        If ((nextTable.Columns.count = 5) And (nextTable.Rows.count >= 2)) Then
            If ((Left(nextTable.Cell(1, 1).Range.Text, 8) = "Revision") And _
                (Left(nextTable.Cell(1, 2).Range.Text, 4) = "Date") And _
                (Left(nextTable.Cell(1, 3).Range.Text, 6) = "Author") And _
                (Left(nextTable.Cell(1, 4).Range.Text, 7) = "Release") And _
                (Left(nextTable.Cell(1, 5).Range.Text, 6) = "Reason")) Then

                isHistoryTableAvailable = True
                Exit For
            End If
        End If
    Next nextTable
End Function
Function isReleaseDocument() As Boolean
    Dim amountRows As Integer
    Dim isRelease As String

    Dim tableId As Integer
    tableId = 2
    
    If (isHistoryTableAvailable() = True) Then
        amountRows = ActiveDocument.Tables.Item(tableId).Rows.count
        isRelease = ActiveDocument.Tables.Item(tableId).Cell(amountRows, 4).Range.Text
        isRelease = Left(isRelease, Len(isRelease) - 2)

        If ((LCase(isRelease) = "true") And (amountRows > 2)) Then
            isReleaseDocument = True
        End If
    End If
End Function
Function getMailFromHistory(indexCorrection As Integer) As String
    Dim amountRows As Integer
    Dim historyName As String
    Dim mail As String

    Dim tableId As Integer
    tableId = 2
    amountRows = ActiveDocument.Tables.Item(tableId).Rows.count

    historyName = ActiveDocument.Tables.Item(tableId).Cell(amountRows - indexCorrection, 3).Range.Text
    historyName = Left(historyName, Len(historyName) - 2)

    nameArray = Split(historyName, " ")
    If (UBound(nameArray) + 1 = 2) Then
        If (InStr(nameArray(0), ",")) Then
            mail = nameArray(1) + "." + Left(nameArray(0), Len(nameArray(0)) - 1)
        Else
            mail = nameArray(0) + "." + nameArray(1)
        End If
            
        mail = LCase(mail + "@continental-corporation.com")
    End If

    getMailFromHistory = mail
End Function
Function getDateFromHistory(indexCorrection As Integer) As String
    Dim amountRows As Integer
    Dim historyDate As String

    Dim tableId As Integer
    tableId = 2
    amountRows = ActiveDocument.Tables.Item(tableId).Rows.count

    historyDate = ActiveDocument.Tables.Item(tableId).Cell(amountRows - indexCorrection, 2).Range.Text
    historyDate = Left(historyDate, Len(historyDate) - 2)
    getDateFromHistory = historyDate
End Function
Function getMailFromAuthor() As String
    Dim mail As String
    Dim author As String
    Dim authorArray() As String

    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(2, 1).Range.Text), 6) = "author" Then
        author = Split(ActiveDocument.Tables.Item(1).Cell(2, 2).Range.Text, Chr$(13))(0)
        authorArray = Split(author, " ")
        
        If (UBound(authorArray) + 1 = 2) Then
            If (InStr(authorArray(0), ",")) Then
                mail = authorArray(1) + "." + Left(authorArray(0), Len(authorArray(0)) - 1)
            Else
                mail = authorArray(0) + "." + authorArray(1)
            End If
            
            mail = LCase(mail + "@continental-corporation.com")
        End If
    End If
    
    getMailFromAuthor = mail
End Function
Function getMailFromReleaser() As String
    Dim mail As String
    Dim releaser As String
    Dim releaserArray() As String

    If Left(LCase(ActiveDocument.Tables.Item(1).Cell(6, 1).Range.Text), 8) = "releaser" Then
        releaser = Split(ActiveDocument.Tables.Item(1).Cell(6, 2).Range.Text, Chr$(13))(0)
        releaserArray = Split(releaser, " ")
        
        If (UBound(releaserArray) + 1 = 2) Then
            If (InStr(releaserArray(0), ",")) Then
                mail = releaserArray(1) + "." + Left(releaserArray(0), Len(releaserArray(0)) - 1)
            Else
                mail = releaserArray(0) + "." + releaserArray(1)
            End If
            
            mail = LCase(mail + "@continental-corporation.com")
        End If
    End If
    
    getMailFromReleaser = mail
End Function
Sub removeReleaseColumnFromHistory()
    Dim nextCell As Cell

    Dim tableId As Integer
    tableId = 2

    For Each nextCell In ActiveDocument.Tables.Item(tableId).Columns.Item(4).Cells
        nextCell.Range.Text = ""
        nextCell.Select
        Selection.MoveRight Unit:=wdCharacter, count:=2, Extend:=wdExtend
        Selection.Cells.Merge
    Next nextCell
End Sub

